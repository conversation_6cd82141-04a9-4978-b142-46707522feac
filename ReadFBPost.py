import requests
import os
import sys
import json
import tempfile
import time
import re
import logging
from dotenv import load_dotenv

load_dotenv() # Load environment variables from .env file

TELEGRAM_VIDEO_LIMIT_BYTES = 50 * 1024 * 1024 # 50 MB (approximate direct URL limit)

# Configure logging
def setup_logging(debug_mode=False):
    """
    Set up logging configuration with appropriate levels and formatting.

    :param debug_mode: If True, set logging level to DEBUG, otherwise INFO
    """
    log_level = logging.DEBUG if debug_mode else logging.INFO

    # Configure root logger
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('facebook_posts.log', encoding='utf-8')
        ]
    )

    # Create logger for this module
    logger = logging.getLogger(__name__)
    return logger

# Initialize logger (will be reconfigured in main)
logger = logging.getLogger(__name__)

# Generic API Helper Functions for DRY principle
class APIConfig:
    """Configuration class for different API endpoints"""

    TELEGRAM = {
        'base_url': 'https://api.telegram.org/bot{token}',
        'endpoints': {
            'message': '/sendMessage',
            'photo': '/sendPhoto',
            'video': '/sendVideo',
            'document': '/sendDocument',
            'info': '/getMe'
        },
        'success_key': 'ok',
        'error_key': 'description'
    }

    WHATSAPP = {
        'base_url': 'https://gate.whapi.cloud/messages',
        'endpoints': {
            'message': '/text',
            'photo': '/image',
            'video': '/video'
        },
        'success_key': 'sent',
        'error_key': 'error'
    }

def make_api_request(config, endpoint, auth_token, payload=None, files=None,
                    timeout=30, service_name="API"):
    """
    Generic function to make API requests with consistent error handling.

    :param config: API configuration dictionary
    :param endpoint: API endpoint to call
    :param auth_token: Authentication token

    :param payload: Request payload
    :param files: Files to upload (for multipart requests)
    :param timeout: Request timeout in seconds
    :param service_name: Service name for logging
    :return: Tuple (success: bool, result: dict)
    """
    try:
        # Build URL
        if config == APIConfig.TELEGRAM:
            url = config['base_url'].format(token=auth_token) + config['endpoints'][endpoint]
            headers = None
            request_data = payload
        else:  # WhatsApp
            url = config['base_url'] + config['endpoints'][endpoint]
            headers = {
                "Authorization": f"Bearer {auth_token}",
                "Accept": "application/json"
            }
            if files:
                # For file uploads, don't set Content-Type (let requests handle it)
                request_data = payload
            else:
                headers["Content-Type"] = "application/json"
                request_data = payload

        # Make request
        if files:
            response = requests.post(url, headers=headers, data=request_data, files=files, timeout=timeout)
        elif config == APIConfig.TELEGRAM:
            response = requests.post(url, data=request_data, timeout=timeout)
        else:
            response = requests.post(url, headers=headers, json=request_data, timeout=timeout)

        response.raise_for_status()

        # Parse response
        result = response.json()

        # Check success based on service
        if config == APIConfig.TELEGRAM:
            success = result.get(config['success_key'], False)
            error_msg = result.get(config['error_key'], 'Unknown error')
        else:  # WhatsApp
            success = result.get(config['success_key'], False)
            error_info = result.get(config['error_key'], {})
            if isinstance(error_info, dict):
                error_msg = error_info.get('message', 'Unknown error')
            else:
                error_msg = str(error_info)

        if success:
            return True, result
        else:
            logger.error(f"{service_name} API error: {error_msg}")
            return False, result

    except requests.exceptions.RequestException as e:
        logger.error(f"Network error with {service_name}: {e}")
        return False, None
    except Exception as e:
        logger.error(f"Unexpected error with {service_name}: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        return False, None

def handle_api_error_details(response_status_code, response_text, service_name):
    """
    Generic function to handle API error details logging.

    :param response_status_code: HTTP status code
    :param response_text: Response text
    :param service_name: Service name for logging
    """
    if response_status_code != 200:
        logger.error(f"{service_name} API Error: Status code {response_status_code}")
        try:
            error_data = json.loads(response_text)
            logger.debug(f"Error details: {json.dumps(error_data, indent=2)}")
        except:
            logger.debug(f"Response text: {response_text}")

def get_media_size_from_url(media_url):
    """
    Get the size of media from its URL using a HEAD request.

    :param media_url: URL of the media.
    :return: Size in bytes if successful, None otherwise.
    """
    try:
        response = requests.head(media_url, timeout=10)
        response.raise_for_status()
        content_length = response.headers.get('content-length')
        if content_length:
            return int(content_length)
    except requests.exceptions.RequestException as e:
        logger.warning(f"Could not get media size from {media_url}: {e}")
    return None

def send_media_with_fallback(config, auth_token, channel_id, media_url, media_type,
                            caption=None, service_name="API", download_func=None,
                            file_send_func=None):
    """
    Generic function to send media with fallback options.

    :param config: API configuration
    :param auth_token: Authentication token
    :param channel_id: Channel ID
    :param media_url: URL of media to send
    :param media_type: Type of media ('photo' or 'video')
    :param caption: Optional caption
    :param service_name: Service name for logging
    :param download_func: Function to download media
    :param file_send_func: Function to send downloaded file
    :return: True if successful, False otherwise
    """
    # Prepare payload based on service
    if config == APIConfig.TELEGRAM:
        payload = {
            'chat_id': channel_id,
            'parse_mode': 'HTML'
        }
        if media_type == 'photo':
            payload['photo'] = media_url
        else:  # video
            payload['video'] = media_url

        if caption:
            payload['caption'] = caption
    else:  # WhatsApp
        payload = {
            "to": channel_id,
            "media": media_url
        }
        if caption:
            payload["caption"] = caption

    # Try direct URL first
    success, _ = make_api_request(config, media_type, auth_token,
                                payload, service_name=service_name)

    if success:
        logger.info(f"{media_type.capitalize()} sent successfully to {service_name}")
        return True

    # If direct sending fails and we have fallback functions, try download-then-upload
    if download_func and file_send_func:
        logger.info(f"Direct {media_type} sending failed, trying download-then-upload...")
        temp_file_path = download_func(media_url)

        if temp_file_path:
            success = file_send_func(auth_token, channel_id, temp_file_path, caption)
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except:
                pass
            return success

    return False

def get_recent_facebook_posts(group_id, access_token, post_limit=2):
    """
    Fetch recent posts from a specific Facebook group using the Graph API.

    :param group_id: The ID of the Facebook group.
    :param access_token: Your Facebook Graph API access token.
    :param post_limit: Number of recent posts to fetch (default is 2).
    :return: List of recent posts.
    """
    # It's good practice to use the latest stable API version.
    # Please check Facebook's developer documentation for the current latest version.
    url = f"https://graph.facebook.com/v22.0/{group_id}/feed"
    params = {
        'access_token': access_token,
        'limit': post_limit,
        'fields': 'id,message,created_time,full_picture,attachments{type,media,target,url,title,description,subattachments}'
    }

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        data = response.json()

        if 'data' in data:
            return data['data']
        else:
            logger.warning("No posts found or insufficient permissions.")
            return []

    except requests.exceptions.RequestException as e:
        logger.error(f"An error occurred while fetching Facebook posts: {e}")
        return []

def load_published_posts(filename="published_posts.txt"):
    """
    Load the list of already published post IDs from a local file.

    :param filename: Name of the file to store published post IDs.
    :return: Set of published post IDs.
    """
    try:
        if os.path.exists(filename):
            with open(filename, 'r') as f:
                return set(line.strip() for line in f if line.strip())
        return set()
    except Exception as e:
        logger.error(f"Error loading published posts: {e}")
        return set()

def save_published_posts(published_posts, filename="published_posts.txt"):
    """
    Save the list of published post IDs to a local file.

    :param published_posts: Set of published post IDs.
    :param filename: Name of the file to store published post IDs.
    """
    try:
        with open(filename, 'w') as f:
            for post_id in published_posts:
                f.write(f"{post_id}\n")
    except Exception as e:
        logger.error(f"Error saving published posts: {e}")

def extract_media_from_post(post):
    """
    Extract media URLs from a Facebook post.

    :param post: Facebook post data.
    :return: List of dictionaries with media URLs and types.
    """
    media_items = []

    # Check for attachments first (more reliable for videos)
    attachments = post.get('attachments', {})
    if isinstance(attachments, dict) and 'data' in attachments:
        for attachment in attachments['data']:
            # Handle video attachments
            if attachment.get('type') in ['video_inline', 'video_autoplay', 'video', 'video_direct_response']:
                # Try different video URL sources
                video_url = None

                # Method 1: Check media.source
                if attachment.get('media') and 'source' in attachment['media']:
                    video_url = attachment['media']['source']

                # Method 2: Check target.url for video links
                elif attachment.get('target') and attachment['target'].get('url'):
                    target_url = attachment['target']['url']
                    # Facebook video URLs often redirect, but we can try
                    if 'facebook.com' in target_url and 'videos' in target_url:
                        video_url = target_url

                # Method 3: Check url field directly
                elif attachment.get('url'):
                    video_url = attachment['url']

                if video_url and not any(item['url'] == video_url for item in media_items):
                    media_items.append({'url': video_url, 'type': 'video'})

            # Handle photo attachments
            elif attachment.get('type') == 'photo' and attachment.get('media'):
                if 'image' in attachment['media'] and 'src' in attachment['media']['image']:
                    media_url = attachment['media']['image']['src']
                    if media_url and not any(item['url'] == media_url for item in media_items):
                        media_items.append({'url': media_url, 'type': 'photo'})

    # Check for full_picture as fallback (often just thumbnail for videos)
    if post.get('full_picture') and not media_items:
        full_picture = post['full_picture']
        if 'safe_image' not in full_picture and len(full_picture) > 50:
            # This is likely just a thumbnail, but better than nothing
            media_type = 'photo'  # Default to photo since we can't get actual video
            media_items.append({'url': full_picture, 'type': media_type})

    return media_items

def extract_actual_url_from_facebook_redirect(facebook_url):
    """
    Extract the actual destination URL from a Facebook redirect URL.

    :param facebook_url: Facebook redirect URL (e.g., l.facebook.com/l.php?u=...)
    :return: The actual destination URL, or the original URL if not a redirect
    """
    try:
        if 'l.facebook.com/l.php' in facebook_url and 'u=' in facebook_url:
            # Parse the URL to extract the 'u' parameter
            from urllib.parse import urlparse, parse_qs, unquote
            parsed = urlparse(facebook_url)
            query_params = parse_qs(parsed.query)
            if 'u' in query_params:
                # The 'u' parameter contains the actual URL (URL-encoded)
                actual_url = unquote(query_params['u'][0])
                return actual_url
    except Exception as e:
        logger.debug(f"Error extracting URL from Facebook redirect: {e}")

    return facebook_url

def extract_links_from_post(post):
    """
    Extract external links from a Facebook post (separate from media).

    :param post: Facebook post data.
    :return: List of dictionaries with link URLs and metadata.
    """
    links = []

    # Check for link/share attachments
    attachments = post.get('attachments', {})
    if isinstance(attachments, dict) and 'data' in attachments:
        for attachment in attachments['data']:
            # Handle link/share attachments (external URLs)
            if attachment.get('type') in ['share', 'link', 'external_url']:
                link_url = None
                title = None
                description = None

                # Try different URL sources
                if attachment.get('target') and attachment['target'].get('url'):
                    link_url = attachment['target']['url']
                elif attachment.get('url'):
                    link_url = attachment['url']

                # Get title and description if available
                if attachment.get('title'):
                    title = attachment['title']
                if attachment.get('description'):
                    description = attachment['description']

                # Only add if it's an external link (not Facebook internal)
                if link_url and not link_url.startswith('https://www.facebook.com') and not link_url.startswith('https://facebook.com'):
                    # Extract actual URL from Facebook redirect if needed
                    actual_url = extract_actual_url_from_facebook_redirect(link_url)

                    # Check if this link is already in our list
                    if not any(item['url'] == actual_url for item in links):
                        link_info = {
                            'url': actual_url,
                            'type': 'link',
                            'title': title,
                            'description': description
                        }
                        links.append(link_info)
                        logger.debug(f"Found external link: {actual_url}")

    return links

def send_photo_to_telegram(bot_token, channel_id, photo_url, caption=None):
    """
    Send a photo to a Telegram channel using HTTP API.

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :param photo_url: URL of the photo to send.
    :param caption: Optional caption for the photo.
    :return: True if successful, False otherwise.
    """
    payload = {
        'chat_id': channel_id,
        'photo': photo_url,
        'parse_mode': 'HTML'
    }

    if caption:
        payload['caption'] = caption

    success, _ = make_api_request(APIConfig.TELEGRAM, 'photo', bot_token,
                                payload, service_name="Telegram")

    if success:
        logger.info("Photo sent successfully to Telegram")

    return success

def send_video_to_telegram(bot_token, channel_id, video_url, caption=None):
    """
    Send a video to a Telegram channel using HTTP API.

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :param video_url: URL of the video to send.
    :param caption: Optional caption for the video.
    :return: True if successful, False otherwise.
    """
    payload = {
        'chat_id': channel_id,
        'video': video_url,
        'parse_mode': 'HTML'
    }

    if caption:
        payload['caption'] = caption

    success, result = make_api_request(APIConfig.TELEGRAM, 'video', bot_token,
                                     payload, service_name="Telegram")

    if success:
        logger.info("Video sent successfully to Telegram")
    else:
        # Log additional details for debugging if we have the result
        if result and 'description' in result:
            error_description = result['description']
            if 'bad request' in error_description.lower():
                logger.debug(f"Video URL: {video_url[:100]}...")
                if 'file size' in error_description.lower():
                    logger.info("Tip: Video file may be too large for Telegram")
                elif 'format' in error_description.lower():
                    logger.info("Tip: Video format may not be supported by Telegram")
                else:
                    logger.info("Tip: Video URL may not be directly accessible by Telegram")

    return success

def download_video(video_url, max_size_mb=2000): # Increased to 2GB for Telegram upload limit
    """
    Download video from URL to a temporary file.

    :param video_url: URL of the video to download
    :param max_size_mb: Maximum file size in MB to download
    :return: Path to temporary file if successful, None if failed
    """
    try:
        # Create a temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp4')
        temp_path = temp_file.name
        temp_file.close()

        # Download with streaming to handle large files
        response = requests.get(video_url, stream=True, timeout=30)
        response.raise_for_status()

        # Check content length if available
        content_length = response.headers.get('content-length')
        if content_length:
            size_mb = int(content_length) / (1024 * 1024)
            if size_mb > max_size_mb:
                logger.warning(f"Video too large: {size_mb:.1f}MB (max: {max_size_mb}MB)")
                os.unlink(temp_path)
                return None

        # Download the file
        downloaded_size = 0
        max_size_bytes = max_size_mb * 1024 * 1024

        with open(temp_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    downloaded_size += len(chunk)
                    if downloaded_size > max_size_bytes:
                        logger.warning(f"Video too large during download: {downloaded_size / (1024 * 1024):.1f}MB")
                        f.close()
                        os.unlink(temp_path)
                        return None
                    f.write(chunk)

        logger.info(f"Video downloaded: {downloaded_size / (1024 * 1024):.1f}MB")
        return temp_path

    except requests.exceptions.RequestException as e:
        logger.error(f"Error downloading video: {e}")
        if 'temp_path' in locals() and os.path.exists(temp_path):
            os.unlink(temp_path)
        return None
    except Exception as e:
        logger.error(f"Unexpected error downloading video: {e}")
        if 'temp_path' in locals() and os.path.exists(temp_path):
            os.unlink(temp_path)
        return None

def send_video_file_to_telegram(bot_token, channel_id, video_file_path, caption=None):
    """
    Send a local video file to Telegram.

    :param bot_token: Telegram bot token
    :param channel_id: Telegram channel ID
    :param video_file_path: Path to the local video file
    :param caption: Optional caption for the video
    :return: True if successful, False otherwise
    """
    try:
        with open(video_file_path, 'rb') as video_file:
            files = {'video': video_file}
            data = {
                'chat_id': channel_id,
                'parse_mode': 'HTML'
            }

            if caption:
                data['caption'] = caption

            success, _ = make_api_request(APIConfig.TELEGRAM, 'video', bot_token,
                                        data, files=files, timeout=120,
                                        service_name="Telegram")

            if success:
                logger.info("Video file uploaded successfully to Telegram")

            return success

    except Exception as e:
        logger.error(f"Error opening video file for Telegram upload: {e}")
        return False

def send_video_as_document(bot_token, channel_id, video_url, caption=None):
    """
    Send a video as a document to Telegram (fallback method).

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :param video_url: URL of the video to send as document.
    :param caption: Optional caption for the document.
    :return: True if successful, False otherwise.
    """
    payload = {
        'chat_id': channel_id,
        'document': video_url,
        'parse_mode': 'HTML'
    }

    if caption:
        payload['caption'] = caption

    success, _ = make_api_request(APIConfig.TELEGRAM, 'document', bot_token,
                                payload, service_name="Telegram")

    if success:
        logger.info("Video sent as document successfully to Telegram")

    return success

def send_link_to_telegram(bot_token, channel_id, message_with_link):
    """
    Send a message with a link to Telegram (fallback for videos that can't be directly sent).

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :param message_with_link: Message containing the link.
    :return: True if successful, False otherwise.
    """
    payload = {
        'chat_id': channel_id,
        'text': message_with_link,
        'parse_mode': 'HTML',
        'disable_web_page_preview': False  # Allow preview for video links
    }

    success, _ = make_api_request(APIConfig.TELEGRAM, 'message', bot_token,
                                payload, service_name="Telegram")

    if success:
        logger.info("Link message sent successfully to Telegram")

    return success

def send_post_to_telegram(bot_token, channel_id, post_message, media_items):
    """
    Send a Facebook post to Telegram, handling both text and media.

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :param post_message: Text content of the post.
    :param media_items: List of dictionaries with media URLs and types.
    :return: True if successful, False otherwise.
    """
    success_count = 0

    # Format the text message
    telegram_message = post_message

    if not media_items:
        # Text-only post
        return send_to_telegram(bot_token, channel_id, telegram_message)

    else:
        # Post with media
        # Send the first media item with the caption (text)
        first_media = media_items[0]
        if first_media['type'] == 'video':
            video_url = first_media['url']
            video_size = get_media_size_from_url(video_url)

            if video_size is not None and video_size > TELEGRAM_VIDEO_LIMIT_BYTES:
                logger.info(f"Video size {video_size / (1024 * 1024):.1f}MB exceeds Telegram direct video limit ({TELEGRAM_VIDEO_LIMIT_BYTES / (1024 * 1024):.1f}MB). Attempting download-then-upload.")
                temp_video_path = download_video(video_url)
                if temp_video_path:
                    success = send_video_file_to_telegram(bot_token, channel_id, temp_video_path, telegram_message)
                    try:
                        os.unlink(temp_video_path)
                    except:
                        pass
                else:
                    success = False # Download failed
                
                if not success:
                    logger.info("Download-upload failed, trying as document...")
                    success = send_video_as_document(bot_token, channel_id, video_url, telegram_message)
                    if not success:
                        logger.info("Document sending also failed, sending as link...")
                        link_message = f"{telegram_message}\n\n🎥 <a href=\"{video_url}\">Click To Watch the Video</a>"
                        success = send_link_to_telegram(bot_token, channel_id, link_message)
            else:
                success = send_video_to_telegram(bot_token, channel_id, video_url, telegram_message)

                # If direct video sending fails, try download-then-upload
                if not success:
                    logger.info("Direct video sending failed, trying download-then-upload...")
                    temp_video_path = download_video(video_url)

                    if temp_video_path:
                        success = send_video_file_to_telegram(bot_token, channel_id, temp_video_path, telegram_message)
                        # Clean up temporary file
                        try:
                            os.unlink(temp_video_path)
                        except:
                            pass

                    # If download-upload also fails, try as document
                    if not success:
                        logger.info("Download-upload failed, trying as document...")
                        success = send_video_as_document(bot_token, channel_id, video_url, telegram_message)

                        # If document also fails, send as link
                        if not success:
                            logger.info("Document sending also failed, sending as link...")
                            link_message = f"{telegram_message}\n\n🎥 <a href=\"{video_url}\">Click To Watch the Video</a>"
                            success = send_link_to_telegram(bot_token, channel_id, link_message)
        else:
            success = send_photo_to_telegram(bot_token, channel_id, first_media['url'], telegram_message)

        if success:
            success_count += 1

        # Send additional media without captions
        for media_item in media_items[1:]:
            if media_item['type'] == 'video':
                video_url = media_item['url']
                video_size = get_media_size_from_url(video_url)

                if video_size is not None and video_size > TELEGRAM_VIDEO_LIMIT_BYTES:
                    logger.info(f"Additional video size {video_size / (1024 * 1024):.1f}MB exceeds Telegram direct video limit. Attempting download-then-upload.")
                    temp_video_path = download_video(video_url)
                    if temp_video_path:
                        success = send_video_file_to_telegram(bot_token, channel_id, temp_video_path)
                        try:
                            os.unlink(temp_video_path)
                        except:
                            pass
                    else:
                        success = False # Download failed

                    if not success:
                        logger.info("Sending additional video as document failed, trying as link...")
                        success = send_video_as_document(bot_token, channel_id, video_url)
                        if not success:
                            link_message = f"🎥 <a href=\"{video_url}\">Click To Watch the Video</a>"
                            success = send_link_to_telegram(bot_token, channel_id, link_message)
                else:
                    success = send_video_to_telegram(bot_token, channel_id, media_item['url'])

                    # If direct video sending fails, try download-then-upload
                    if not success:
                        temp_video_path = download_video(media_item['url'])

                        if temp_video_path:
                            success = send_video_file_to_telegram(bot_token, channel_id, temp_video_path)
                            # Clean up temporary file
                            try:
                                os.unlink(temp_video_path)
                            except:
                                pass

                        # If download-upload also fails, try as document, then as link
                        if not success:
                            success = send_video_as_document(bot_token, channel_id, media_item['url'])

                            if not success:
                                link_message = f"🎥 <a href=\"{media_item['url']}\">Click To Watch the Video</a>"
                                success = send_link_to_telegram(bot_token, channel_id, link_message)
            else:
                success = send_photo_to_telegram(bot_token, channel_id, media_item['url'])

            if success:
                success_count += 1

        # Consider it successful if at least the main content was sent
        return success_count > 0

def send_to_telegram(bot_token, channel_id, message):
    """
    Send a message to a Telegram channel using HTTP API.

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :param message: Message to send.
    :return: True if successful, False otherwise.
    """
    payload = {
        'chat_id': channel_id,
        'text': message,
        'parse_mode': 'HTML'
    }

    success, result = make_api_request(APIConfig.TELEGRAM, 'message', bot_token,
                                     payload, service_name="Telegram")

    if success:
        logger.info("Message sent successfully to Telegram")
    else:
        # Provide helpful tips based on common errors
        if result and 'description' in result:
            error_description = result['description']
            if "chat not found" in error_description.lower():
                logger.info("Tip: Make sure the channel ID is correct and the bot is added to the channel")
            elif "forbidden" in error_description.lower():
                logger.info("Tip: Make sure the bot has permission to send messages in the channel")
            elif "bot was blocked" in error_description.lower():
                logger.info("Tip: The bot was blocked. Make sure it's added as an admin to the channel")

    return success

def test_telegram_connection(bot_token, channel_id):
    """
    Test the Telegram bot connection and permissions using HTTP API.

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :return: True if connection is successful, False otherwise.
    """
    # Test bot info first
    success, result = make_api_request(APIConfig.TELEGRAM, 'info', bot_token,
                                     service_name="Telegram")

    if success and result:
        bot_info = result.get('result', {})
        username = bot_info.get('username', 'Unknown')
        first_name = bot_info.get('first_name', 'Unknown')
        logger.info(f"Bot connected: @{username} ({first_name})")
    else:
        logger.error("Failed to get bot info")
        return False

    # Test sending a simple message
    test_message = "🧪 <b>Test Message</b>\n\nThis is a test message from your Facebook Posts bot."
    if send_to_telegram(bot_token, channel_id, test_message):
        logger.info("Test message sent successfully!")
        return True
    else:
        logger.error("Failed to send test message")
        return False

def send_image_file_to_whats(api_key, channel_id, image_file_path, caption=None):
    """
    Send a local image file to Whatsapp.

    :param api_key: Whatsapp api key.
    :param channel_id: Whatsapp channel ID.
    :param image_file_path: Path to the local image file
    :param caption: Optional caption for the image
    :return: True if successful, False otherwise
    """
    try:
        # Check if file exists
        if not os.path.exists(image_file_path):
            logger.error(f"Image file not found: {image_file_path}")
            return False

        with open(image_file_path, 'rb') as image_file:
            files = {
                'file': ('image.jpg', image_file, 'image/jpeg')
            }

            data = {
                "to": channel_id
            }

            if caption:
                data["caption"] = caption

            success, _ = make_api_request(APIConfig.WHATSAPP, 'photo', api_key,
                                        data, files=files, service_name="WhatsApp", timeout=60)

            if success:
                logger.info("Image file sent successfully to WhatsApp")

            return success

    except Exception as e:
        logger.error(f"Error opening image file for WhatsApp upload: {e}")
        return False

def send_post_to_whats(api_key, channel_id, post_message, media_items):
    """
    Send a Facebook post to Whatsapp, handling both text and media.

    :param api_key: Whatsapp api key.
    :param channel_id: Whatsapp channel ID.
    :param post_message: Text content of the post.
    :param media_items: List of dictionaries with media URLs and types.
    :return: True if successful, False otherwise.
    """
    try:
        # If there are no media items, just send the text message
        if not media_items:
            return send_to_whats(api_key, channel_id, post_message)
            
        # Process the first media item (WhatsApp usually supports one media per message)
        media_item = media_items[0]
        media_type = media_item.get('type')
        media_url = media_item.get('url')
        
        if not media_url:
            # If media URL is missing, fall back to text message
            return send_to_whats(api_key, channel_id, post_message)
            
        if media_type == 'photo':
            # 1. Try to send photo directly from URL
            success = send_photo_to_whats(api_key, channel_id, media_url, post_message)
            if success:
                logger.info("Photo sent successfully to WhatsApp with caption")
                return True

            # 2. If direct URL fails, download and send as file
            logger.info("Direct photo URL failed, attempting to download image and send as file...")
            image_path = download_image(media_url)
            if image_path:
                success = send_image_file_to_whats(api_key, channel_id, image_path, post_message)
                # Clean up the temporary file
                try:
                    os.unlink(image_path)
                except:
                    pass
                if success:
                    logger.info("Downloaded photo sent successfully to WhatsApp with caption")
                return success
            else:
                # If image download fails, send just the text
                logger.warning("Image download failed, sending text only as fallback")
                return send_to_whats(api_key, channel_id, post_message)
                
        elif media_type == 'video':
            # 1. Try to send directly from URL
            success = send_video_to_whats(api_key, channel_id, media_url, post_message)
            if success:
                return True
                
            # 2. If direct URL fails, download and send as file
            logger.info("Attempting to download video and send as file...")
            video_path = download_video(media_url)
            if video_path:
                success = send_video_file_to_whats(api_key, channel_id, video_path, post_message)
                # Clean up the temporary file
                try:
                    os.unlink(video_path)
                except:
                    pass
                return success
            else:
                # If video download fails, send just the text
                logger.warning("Video download failed, sending text only")
                return send_to_whats(api_key, channel_id, post_message)
        else:
            # Unknown media type, send as text
            return send_to_whats(api_key, channel_id, post_message)
            
    except Exception as e:
        logger.error(f"Error sending post to WhatsApp: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        return False

def send_to_whats(api_key, channel_id, message):
    """
    Send a message to a Whatsapp channel using HTTP API.

    :param api_key: Whatsapp api key.
    :param channel_id: Whatsapp channel ID.
    :param message: Message to send.
    :return: True if successful, False otherwise.
    """
    payload = {
        "to": channel_id,
        "body": message
    }

    success, _ = make_api_request(APIConfig.WHATSAPP, 'message', api_key,
                                payload, service_name="WhatsApp", timeout=60)

    if success:
        logger.info("Message sent successfully to WhatsApp")

    return success

def send_photo_to_whats(api_key, channel_id, photo_url, caption=None):
    """
    Send a photo to a Whatsapp channel using HTTP API.

    :param api_key: Whatsapp api key.
    :param channel_id: Whatsapp channel ID.
    :param photo_url: URL of the photo to send.
    :param caption: Optional caption for the photo.
    :return: True if successful, False otherwise.
    """
    payload = {
        "to": channel_id,
        "media": photo_url
    }

    if caption:
        payload["caption"] = caption

    success, _ = make_api_request(APIConfig.WHATSAPP, 'photo', api_key,
                                payload, service_name="WhatsApp", timeout=60)

    if success:
        logger.info("Photo sent successfully to WhatsApp")

    return success

def send_video_file_to_whats(api_key, channel_id, video_file_path, caption=None):
    """
    Send a local video file to Whatsapp.

    :param api_key: Whatsapp api key.
    :param channel_id: Whatsapp channel ID.
    :param video_file_path: Path to the local video file
    :param caption: Optional caption for the video
    :return: True if successful, False otherwise
    """
    try:
        # Check if file exists
        if not os.path.exists(video_file_path):
            logger.error(f"Video file not found: {video_file_path}")
            return False

        with open(video_file_path, 'rb') as video_file:
            files = {
                'file': ('video.mp4', video_file, 'video/mp4')
            }

            data = {
                "to": channel_id
            }

            if caption:
                data["caption"] = caption

            success, _ = make_api_request(APIConfig.WHATSAPP, 'video', api_key,
                                        data, files=files, service_name="WhatsApp", timeout=60)

            if success:
                logger.info("Video file sent successfully to WhatsApp")

            return success

    except Exception as e:
        logger.error(f"Error opening video file for WhatsApp upload: {e}")
        return False
def send_video_to_whats(api_key, channel_id, video_url, caption=None):
    """
    Send a video to a Whatsapp channel using HTTP API.

    :param api_key: Whatsapp api key.
    :param channel_id: Whatsapp channel ID.
    :param video_url: URL of the video to send.
    :param caption: Optional caption for the video.
    :return: True if successful, False otherwise.
    """
    payload = {
        "to": channel_id,
        "media": video_url
    }

    if caption:
        payload["caption"] = caption

    success, _ = make_api_request(APIConfig.WHATSAPP, 'video', api_key,
                                payload, service_name="WhatsApp", timeout=60)

    if success:
        logger.info("Video sent successfully to WhatsApp")

    return success

def download_image(image_url, max_size_mb=5):
    """
    Download image from URL to a temporary file.

    :param image_url: URL of the image to download
    :param max_size_mb: Maximum file size in MB to download
    :return: Path to temporary file if successful, None if failed
    """
    try:
        # Create a temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
        temp_path = temp_file.name
        temp_file.close()

        # Download with streaming to handle large files
        response = requests.get(image_url, stream=True, timeout=30)
        response.raise_for_status()

        # Check content length if available
        content_length = response.headers.get('content-length')
        if content_length:
            size_mb = int(content_length) / (1024 * 1024)
            if size_mb > max_size_mb:
                logger.warning(f"Image too large: {size_mb:.1f}MB (max: {max_size_mb}MB)")
                os.unlink(temp_path)
                return None

        # Download the file
        downloaded_size = 0
        max_size_bytes = max_size_mb * 1024 * 1024

        with open(temp_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    downloaded_size += len(chunk)
                    if downloaded_size > max_size_bytes:
                        logger.warning(f"Image too large during download: {downloaded_size / (1024 * 1024):.1f}MB")
                        f.close()
                        os.unlink(temp_path)
                        return None
                    f.write(chunk)

        logger.info(f"Image downloaded: {downloaded_size / (1024 * 1024):.1f}MB")
        return temp_path

    except requests.exceptions.RequestException as e:
        logger.error(f"Error downloading image: {e}")
        if 'temp_path' in locals() and os.path.exists(temp_path):
            os.unlink(temp_path)
        return None
    except Exception as e:
        logger.error(f"Unexpected error downloading image: {e}")
        if 'temp_path' in locals() and os.path.exists(temp_path):
            os.unlink(temp_path)
        return None
    
def extract_and_replace_kilya_link(post):
    """
    If the post contains a link to kilya.org.il (in message or attachments),
    return the message with all such links replaced by the special link.
    If no such link, return the original message.
    """
    special_link = '<a href="https://kilya.org.il/he/bmi-form/?utm_source=telegramch">בדקו התאמה</a>'
    message = post.get('message', '')
    # Replace kilya.org.il links in the message
    kilya_pattern = r'https?://\S*kilya\.org\.il\S*'
    if re.search(kilya_pattern, message):
        message = re.sub(kilya_pattern, special_link, message)
        return message, True
    # Check in attachments (links)
    attachments = post.get('attachments', {})
    if isinstance(attachments, dict) and 'data' in attachments:
        for attachment in attachments['data']:
            url = attachment.get('url')
            if url and 'kilya.org.il' in url:
                # If the message doesn't already have the special link, append it
                if special_link not in message:
                    message = f"{message}\n{special_link}" if message else special_link
                return message, True
    return message, False
def test_whatsapp_api(api_key, channel_id):
    """
    Test the WhatsApp API connection by sending a simple text message.
    
    :param api_key: WhatsApp API key
    :param channel_id: WhatsApp channel ID
    :return: True if successful, False otherwise
    """
    logger.info("--- Testing WhatsApp API Connection ---")

    # Test text message
    logger.info("Testing text message...")
    text_result = send_to_whats(api_key, channel_id, "🧪 This is a test message from the WhatsApp API integration")

    # Test image message with URL
    logger.info("Testing image message with URL...")
    image_result = send_photo_to_whats(api_key, channel_id,
                                      "https://upload.wikimedia.org/wikipedia/commons/thumb/1/19/WhatsApp_logo-color-vertical.svg/2048px-WhatsApp_logo-color-vertical.svg.png",
                                      "Test image caption")

    # Overall result
    if text_result or image_result:
        logger.info("WhatsApp API test partially successful")
        return True
    else:
        logger.error("WhatsApp API test failed completely")
        return False
        
if __name__ == "__main__":
    # Check for test mode
    # Check for command line arguments
    test_mode = False
    debug_mode = False
    force_mode = False
    test_api_mode = False
    
    for arg in sys.argv[1:]:
        if arg == "--test":
            test_mode = True
        elif arg == "--debug":
            debug_mode = True
        elif arg == "--force":
            force_mode = True
        elif arg == "--test-api":
            test_api_mode = True
    
    # Setup logging based on debug mode
    logger = setup_logging(debug_mode)

    if debug_mode:
        logger.info("Debug mode enabled - will print detailed API information")

    # Load credentials from environment variables
    GROUP_ID = os.getenv("GROUP_ID")
    ACCESS_TOKEN = os.getenv("ACCESS_TOKEN")
    TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
    TELEGRAM_CHANNEL_ID = os.getenv("TELEGRAM_CHANNEL_ID")
    WHATSAPP_API_KEY = os.getenv("API_KEY")
    WHATSAPP_CHANNEL_ID = os.getenv("WHATSAPP_CHANNEL_ID")

    # Check required Facebook credentials
    if not GROUP_ID or not ACCESS_TOKEN:
        logger.error("GROUP_ID and ACCESS_TOKEN must be set in the .env file.")
        sys.exit(1)

    if not WHATSAPP_API_KEY or not WHATSAPP_CHANNEL_ID:
        logger.warning("API_KEY and WHATSAPP_CHANNEL_ID not set in .env file. Posts will not be published to Whatsapp.")
        whatsapp_enabled = False
    else:
        whatsapp_enabled = True
        if debug_mode:
            logger.debug(f"WhatsApp API Key: {WHATSAPP_API_KEY[:5]}...{WHATSAPP_API_KEY[-5:]}")
            logger.debug(f"WhatsApp Channel ID: {WHATSAPP_CHANNEL_ID}")

    # If test API mode is enabled, just test the WhatsApp API and exit
    if test_api_mode:
        if whatsapp_enabled:
            test_whatsapp_api(WHATSAPP_API_KEY, WHATSAPP_CHANNEL_ID)
        else:
            logger.error("Cannot test WhatsApp API: API_KEY and WHATSAPP_CHANNEL_ID must be set in .env file")
        sys.exit(0)
    

    # Check Telegram credentials
    if not TELEGRAM_BOT_TOKEN or not TELEGRAM_CHANNEL_ID:
        logger.warning("TELEGRAM_BOT_TOKEN and TELEGRAM_CHANNEL_ID not set. Posts will not be published to Telegram.")
        telegram_enabled = False
    else:
        telegram_enabled = True

        # Test Telegram connection if in test mode
        if test_mode:
            if test_telegram_connection(TELEGRAM_BOT_TOKEN, TELEGRAM_CHANNEL_ID):
                logger.info("Telegram test completed successfully!")
            else:
                logger.error("Telegram test failed!")
            sys.exit(0)

    # Load previously published posts
    published_posts = load_published_posts()
    if debug_mode:
        logger.debug(f"Previously published posts: {len(published_posts)}")

    # Fetch the 2 most recent posts
    recent_posts = get_recent_facebook_posts(GROUP_ID, ACCESS_TOKEN)
    if debug_mode:
        logger.debug(f"Fetched {len(recent_posts)} recent posts from Facebook")

    # Reverse the order of posts so the most recent Facebook post
    # appears as the latest message in Telegram
    recent_posts_reversed = list(reversed(recent_posts))

    # Process each post (now in reverse chronological order for Telegram)
    new_posts_published = False
    for i, post in enumerate(recent_posts_reversed, start=1):
        post_id = post.get('id', '')
        post_message = post.get('message', 'No message content')

        if debug_mode:
            logger.debug(f"--- Processing post {i} (ID: {post_id}) ---")
            logger.debug(f"Message: {post_message[:100]}..." if len(post_message) > 100 else f"Message: {post_message}")

        # Extract media from the post
        media_items = extract_media_from_post(post)
        if debug_mode and media_items:
            for item in media_items:
                logger.debug(f"Media: {item['type']} - {item['url'][:50]}..." if len(item['url']) > 50 else f"Media: {item['type']} - {item['url']}")

        # Extract links from the post
        link_items = extract_links_from_post(post)
        if debug_mode and link_items:
            for item in link_items:
                logger.debug(f"Link: {item['url'][:50]}..." if len(item['url']) > 50 else f"Link: {item['url']}")

        # Replace kilya.org.il links if present
        post_message, replaced = extract_and_replace_kilya_link(post)
        if debug_mode and replaced:
            logger.debug("Replaced kilya.org.il links in message")

        # Prepare messages for different platforms
        telegram_message = post_message
        whatsapp_message = post_message

        # Add extracted links to the messages if they're not already in the text
        if link_items:
            for link_item in link_items:
                link_url = link_item['url']
                # Check if the link is already in the message text
                if link_url not in post_message:
                    # Format for Telegram (supports HTML)
                    if link_item.get('title'):
                        telegram_link_text = f"\n\n🔗 <a href=\"{link_url}\">{link_item['title']}</a>"
                    else:
                        telegram_link_text = f"\n\n🔗 {link_url}"
                    telegram_message += telegram_link_text

                    # Format for WhatsApp (plain text only)
                    if link_item.get('title'):
                        whatsapp_link_text = f"\n\n🔗 {link_item['title']}\n{link_url}"
                    else:
                        whatsapp_link_text = f"\n\n🔗 {link_url}"
                    whatsapp_message += whatsapp_link_text

                    if debug_mode:
                        logger.debug(f"Added link to messages: {link_url}")

        # Check if this post has already been published to Telegram
        if post_id and post_id not in published_posts:
            logger.info(f"Publishing new post {post_id}")
            if debug_mode:
                logger.debug(f"Telegram message length: {len(telegram_message)}")
                logger.debug(f"WhatsApp message length: {len(whatsapp_message)}")

            if telegram_enabled:
                # Send to Telegram with media support - only mark as published if successful
                start_telegram = time.time()
                logger.info("Sending to Telegram...")
                telegram_success = send_post_to_telegram(TELEGRAM_BOT_TOKEN, TELEGRAM_CHANNEL_ID, telegram_message, media_items)
                end_telegram = time.time()
                if debug_mode:
                    logger.debug(f"Telegram post took {end_telegram - start_telegram:.2f} seconds")
                if telegram_success:
                    start_whatsapp = time.time()
                    logger.info("Sending to WhatsApp...")
                    whatsapp_success = send_post_to_whats(WHATSAPP_API_KEY, WHATSAPP_CHANNEL_ID, whatsapp_message, media_items)
                    end_whatsapp = time.time()
                    if debug_mode:
                        logger.debug(f"WhatsApp post took {end_whatsapp - start_whatsapp:.2f} seconds")
                    if whatsapp_success:
                        published_posts.add(post_id)
                        new_posts_published = True
                        logger.info(f"Successfully published post {post_id}")
                    else:
                        logger.error(f"Failed to send post {post_id} to WhatsApp")
                else:
                    logger.error(f"Failed to send post {post_id} to Telegram")
        else:
            if post_id:
                logger.debug(f"Post {post_id} already published, skipping")
            else:
                logger.warning("Post has no ID, skipping")

    # Save updated published posts list
    if new_posts_published:
        save_published_posts(published_posts)
        if debug_mode:
            logger.debug(f"Saved {len(published_posts)} published posts")
    else:
        if debug_mode:
            logger.debug("No new posts published")